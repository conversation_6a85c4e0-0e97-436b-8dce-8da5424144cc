{"cells": [{"cell_type": "code", "execution_count": 2, "id": "dc25b046", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pymodbus\n", "  Downloading pymodbus-3.10.0-py3-none-any.whl.metadata (15 kB)\n", "Downloading pymodbus-3.10.0-py3-none-any.whl (163 kB)\n", "   ---------------------------------------- 0.0/163.5 kB ? eta -:--:--\n", "   -- ------------------------------------- 10.2/163.5 kB ? eta -:--:--\n", "   ------- ------------------------------- 30.7/163.5 kB 435.7 kB/s eta 0:00:01\n", "   -------------- ------------------------ 61.4/163.5 kB 544.7 kB/s eta 0:00:01\n", "   -------------------------- ----------- 112.6/163.5 kB 731.4 kB/s eta 0:00:01\n", "   -------------------------------------- 163.5/163.5 kB 819.3 kB/s eta 0:00:00\n", "Installing collected packages: pymodbus\n", "Successfully installed pymodbus-3.10.0\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install pymodbus"]}, {"cell_type": "code", "execution_count": null, "id": "41911904", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "ModbusServerContext.__init__() got an unexpected keyword argument 'store'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 39\u001b[0m\n\u001b[0;32m     37\u001b[0m \u001b[38;5;66;03m# Tạo context cho server\u001b[39;00m\n\u001b[0;32m     38\u001b[0m store \u001b[38;5;241m=\u001b[39m {\u001b[38;5;241m0x03\u001b[39m: data_block}  \u001b[38;5;66;03m# 0x03 là function code cho đ<PERSON>c holding registers\u001b[39;00m\n\u001b[1;32m---> 39\u001b[0m context \u001b[38;5;241m=\u001b[39m ModbusServerContext(store\u001b[38;5;241m=\u001b[39mstore, single\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m     41\u001b[0m \u001b[38;5;66;03m# Khởi tạo và chạy server Modbus TCP\u001b[39;00m\n\u001b[0;32m     42\u001b[0m server \u001b[38;5;241m=\u001b[39m ModbusTcpServer(context, address\u001b[38;5;241m=\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlocalhost\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m5020\u001b[39m))\n", "\u001b[1;31mTypeError\u001b[0m: ModbusServerContext.__init__() got an unexpected keyword argument 'store'"]}], "source": ["\n", "import struct\n", "\n", "def float_to_regs(value):\n", "    # Convert float to 2 registers (big endian)\n", "    b = struct.pack('>f', value)\n", "    return [int.from_bytes(b[:2], 'big'), int.from_bytes(b[2:], 'big')]\n", "\n", "# Khởi tạo các giá trị ví dụ\n", "values = {\n", "    0: float_to_regs(12.3),     # Silica\n", "    2: float_to_regs(50.0),     # Flow permeate\n", "    4: float_to_regs(2.3),      # Flow concentrate\n", "    6: float_to_regs(10.0),     # Concentration factor\n", "    8: float_to_regs(1.5),      # Absorbance\n", "    10: float_to_regs(2.1),     # Photometer voltage\n", "    12: float_to_regs(300),     # Signal period\n", "    14: float_to_regs(99),      # Remaining reagents\n", "    16: float_to_regs(30),      # SilTrace Temperature\n", "    18: float_to_regs(80),      # SilTrace PWM\n", "    20: float_to_regs(32),      # Case temperature\n", "    22: float_to_regs(4.5),     # Signal Output 1\n", "    24: float_to_regs(4.9),     # Signal Output 2\n", "    26: [1234, 0],              # INSTRUMENT_STATUS (gi<PERSON> lập giá trị int)\n", "}\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}