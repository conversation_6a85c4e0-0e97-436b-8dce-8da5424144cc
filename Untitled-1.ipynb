%pip install pymodbus


import struct

def float_to_regs(value):
    # Convert float to 2 registers (big endian)
    b = struct.pack('>f', value)
    return [int.from_bytes(b[:2], 'big'), int.from_bytes(b[2:], 'big')]

# Khởi tạo các giá trị ví dụ
values = {
    0: float_to_regs(12.3),     # Silica
    2: float_to_regs(50.0),     # Flow permeate
    4: float_to_regs(2.3),      # Flow concentrate
    6: float_to_regs(10.0),     # Concentration factor
    8: float_to_regs(1.5),      # Absorbance
    10: float_to_regs(2.1),     # Photometer voltage
    12: float_to_regs(300),     # Signal period
    14: float_to_regs(99),      # Remaining reagents
    16: float_to_regs(30),      # SilTrace Temperature
    18: float_to_regs(80),      # SilTrace PWM
    20: float_to_regs(32),      # Case temperature
    22: float_to_regs(4.5),     # Signal Output 1
    24: float_to_regs(4.9),     # Signal Output 2
    26: [1234, 0],              # INSTRUMENT_STATUS (gi<PERSON> lập giá trị int)
}
