#!/usr/bin/env python3
"""
Modbus TCP Client Test
Test client để đọc dữ liệu từ Modbus simulator
"""

import struct
import time
from pymodbus.client.sync import ModbusTcpClient
import logging

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

def regs_to_float(reg1, reg2):
    """Convert 2 registers back to float"""
    b = reg1.to_bytes(2, 'big') + reg2.to_bytes(2, 'big')
    return struct.unpack('>f', b)[0]

class ModbusTestClient:
    def __init__(self, host='localhost', port=502):
        self.host = host
        self.port = port
        self.client = ModbusTcpClient(host, port=port)
        
        # Đ<PERSON><PERSON> nghĩa tên các thông số
        self.parameter_names = {
            0: "Silica",
            2: "Flow permeate", 
            4: "Flow concentrate",
            6: "Concentration factor",
            8: "Absorbance",
            10: "Photometer voltage",
            12: "Signal period",
            14: "Remaining reagents",
            16: "SilTrace Temperature",
            18: "SilTrace PWM", 
            20: "Case temperature",
            22: "Signal Output 1",
            24: "Signal Output 2",
            26: "INSTRUMENT_STATUS"
        }
    
    def connect(self):
        """Kết nối đến Modbus server"""
        try:
            result = self.client.connect()
            if result:
                log.info(f"Connected to Modbus server at {self.host}:{self.port}")
                return True
            else:
                log.error(f"Failed to connect to Modbus server at {self.host}:{self.port}")
                return False
        except Exception as e:
            log.error(f"Connection error: {e}")
            return False
    
    def disconnect(self):
        """Ngắt kết nối"""
        self.client.close()
        log.info("Disconnected from Modbus server")
    
    def read_float_value(self, address):
        """Đọc giá trị float từ 2 registers"""
        try:
            # Đọc 2 registers liên tiếp
            result = self.client.read_holding_registers(address, 2)
            
            if result.isError():
                log.error(f"Error reading registers at address {address}: {result}")
                return None
            
            # Chuyển đổi 2 registers thành float
            reg1, reg2 = result.registers
            float_value = regs_to_float(reg1, reg2)
            
            return float_value
            
        except Exception as e:
            log.error(f"Exception reading float at address {address}: {e}")
            return None
    
    def read_int_value(self, address):
        """Đọc giá trị integer từ 1 register"""
        try:
            result = self.client.read_holding_registers(address, 1)
            
            if result.isError():
                log.error(f"Error reading register at address {address}: {result}")
                return None
            
            return result.registers[0]
            
        except Exception as e:
            log.error(f"Exception reading int at address {address}: {e}")
            return None
    
    def read_all_values(self):
        """Đọc tất cả các giá trị"""
        log.info("Reading all values from Modbus server:")
        log.info("-" * 50)
        
        for address, name in self.parameter_names.items():
            if address == 26:  # INSTRUMENT_STATUS - integer
                value = self.read_int_value(address)
                if value is not None:
                    log.info(f"Address {address:2d}: {name:<25} = {value}")
                else:
                    log.error(f"Address {address:2d}: {name:<25} = ERROR")
            else:  # Float values
                value = self.read_float_value(address)
                if value is not None:
                    log.info(f"Address {address:2d}: {name:<25} = {value:.3f}")
                else:
                    log.error(f"Address {address:2d}: {name:<25} = ERROR")
        
        log.info("-" * 50)
    
    def continuous_read(self, interval=2):
        """Đọc liên tục với khoảng thời gian nhất định"""
        log.info(f"Starting continuous read every {interval} seconds. Press Ctrl+C to stop.")
        
        try:
            while True:
                self.read_all_values()
                time.sleep(interval)
                
        except KeyboardInterrupt:
            log.info("Continuous read stopped by user")

def main():
    """Hàm main"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Modbus TCP Test Client')
    parser.add_argument('--host', default='localhost', help='Server host address (default: localhost)')
    parser.add_argument('--port', type=int, default=502, help='Server port (default: 502)')
    parser.add_argument('--continuous', '-c', action='store_true', 
                       help='Continuous reading mode')
    parser.add_argument('--interval', type=int, default=2,
                       help='Read interval in seconds for continuous mode (default: 2)')
    
    args = parser.parse_args()
    
    # Tạo client
    client = ModbusTestClient(host=args.host, port=args.port)
    
    try:
        # Kết nối
        if not client.connect():
            return
        
        if args.continuous:
            # Chế độ đọc liên tục
            client.continuous_read(args.interval)
        else:
            # Đọc một lần
            client.read_all_values()
            
    except Exception as e:
        log.error(f"Client error: {e}")
    finally:
        client.disconnect()

if __name__ == "__main__":
    main()
