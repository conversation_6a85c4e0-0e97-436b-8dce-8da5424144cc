# Modbus TCP/RTU Simulator

Thiết bị giả lập Modbus TCP và RTU over TCP Server trả về dữ liệu từ dictionary values đã định nghĩa.

## Cài đặt

```bash
pip install -r requirements.txt
```

## Sử dụng

### 1. Khởi động Modbus Server

```bash
# Chạy Modbus TCP với cấu hình mặc định (localhost:502)
python modbus_simulator.py

# Chạy Modbus RTU over TCP
python modbus_simulator.py --protocol rtu

# Chạy với host và port tùy chỉnh
python modbus_simulator.py --host 0.0.0.0 --port 5020 --protocol tcp

# Chạy RTU over TCP với IP local
python modbus_simulator.py --host ************ --port 502 --protocol rtu

# Chạy với khoảng thời gian cập nhật tùy chỉnh (mặc định 5 giây)
python modbus_simulator.py --update-interval 10 --protocol tcp
```

### 2. Test với Client

Mở terminal khác và chạy:

```bash
# Đọc một lần tất cả giá trị (Modbus TCP)
python modbus_client_test.py

# Đọc từ RTU over TCP server
python modbus_client_test.py --protocol rtu

# Đọc liên tục mỗi 2 giây
python modbus_client_test.py --continuous --protocol tcp

# Kết nối đến RTU over TCP server khác
python modbus_client_test.py --host ************ --port 502 --protocol rtu

# Đọc liên tục với khoảng thời gian tùy chỉnh
python modbus_client_test.py --continuous --interval 5 --protocol tcp
```

## Dữ liệu có sẵn

Server cung cấp các thông số sau tại các địa chỉ Holding Registers:

| Địa chỉ | Tên thông số           | Kiểu dữ liệu | Giá trị mặc định |
|---------|------------------------|--------------|------------------|
| 0-1     | Silica                 | Float        | 12.3             |
| 2-3     | Flow permeate          | Float        | 50.0             |
| 4-5     | Flow concentrate       | Float        | 2.3              |
| 6-7     | Concentration factor   | Float        | 10.0             |
| 8-9     | Absorbance            | Float        | 1.5              |
| 10-11   | Photometer voltage     | Float        | 2.1              |
| 12-13   | Signal period          | Float        | 300              |
| 14-15   | Remaining reagents     | Float        | 99               |
| 16-17   | SilTrace Temperature   | Float        | 30               |
| 18-19   | SilTrace PWM          | Float        | 80               |
| 20-21   | Case temperature       | Float        | 32               |
| 22-23   | Signal Output 1        | Float        | 4.5              |
| 24-25   | Signal Output 2        | Float        | 4.9              |
| 26-27   | INSTRUMENT_STATUS      | Integer      | 1234             |

**Lưu ý:**
- Các giá trị float được lưu trữ trong 2 registers liên tiếp (big-endian format)
- Giá trị INSTRUMENT_STATUS là integer được lưu trong 1 register
- Server tự động cập nhật các giá trị với biến động ngẫu nhiên ±5% mỗi 5 giây (có thể tùy chỉnh)

## Protocol Support

### Modbus TCP (mặc định)
- Sử dụng MBAP header (7 bytes)
- Không có CRC checksum
- Frame format: `[MBAP Header][Function Code][Data]`
- Phù hợp cho hầu hết các ứng dụng Modbus TCP

### RTU over TCP
- Giữ nguyên format RTU serial qua TCP
- Có CRC checksum
- Frame format: `[Device ID][Function Code][Data][CRC]`
- Phù hợp cho các thiết bị yêu cầu RTU format qua TCP

## Tính năng

- **Dual Protocol Support**: Hỗ trợ cả Modbus TCP và RTU over TCP
- **Dữ liệu động**: Các giá trị được cập nhật với biến động ngẫu nhiên
- **Cấu hình linh hoạt**: Có thể thay đổi host, port, protocol và khoảng thời gian cập nhật
- **Logging**: Ghi log chi tiết các hoạt động
- **Test client**: Có sẵn client để test và đọc dữ liệu với cả hai protocol

## Sử dụng với các công cụ khác

Bạn có thể sử dụng các công cụ Modbus khác để kết nối đến simulator:

- **ModbusPoll**: Công cụ GUI để đọc/ghi Modbus
- **QModMaster**: Công cụ mã nguồn mở
- **Python pymodbus**: Thư viện Python
- **Node-RED**: Với node modbus-contrib

## Ví dụ đọc dữ liệu với Python

```python
from pymodbus.client.sync import ModbusTcpClient
import struct

def regs_to_float(reg1, reg2):
    b = reg1.to_bytes(2, 'big') + reg2.to_bytes(2, 'big')
    return struct.unpack('>f', b)[0]

client = ModbusTcpClient('localhost', port=502)
client.connect()

# Đọc Silica (địa chỉ 0-1)
result = client.read_holding_registers(0, 2)
if not result.isError():
    silica_value = regs_to_float(result.registers[0], result.registers[1])
    print(f"Silica: {silica_value}")

client.close()
```

## Troubleshooting

1. **Lỗi "Address already in use"**: Port 502 đã được sử dụng, thử port khác
2. **Lỗi kết nối**: Kiểm tra firewall và địa chỉ IP
3. **Quyền truy cập port 502**: Trên Linux/Mac cần sudo để sử dụng port < 1024
