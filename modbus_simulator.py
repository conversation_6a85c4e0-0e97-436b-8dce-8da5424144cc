#!/usr/bin/env python3
"""
Modbus TCP Server Simulator
<PERSON><PERSON> phỏng thiết bị Modbus trả về dữ liệu từ dictionary values
"""

import struct
import time
import random
import asyncio
from pymodbus.server import StartAsyncTcpServer
from pymodbus.device import ModbusDeviceIdentification
from pymodbus.datastore import ModbusSequentialDataBlock, ModbusSlaveContext, ModbusServerContext
from pymodbus.framer import ModbusRtuFramer, ModbusTcpFramer
import logging

# Cấu hình logging
logging.basicConfig(level=logging.INFO) 
log = logging.getLogger(__name__)

def float_to_regs(value):
    """Convert float to 2 registers (big endian)"""
    b = struct.pack('>f', value)
    return [int.from_bytes(b[:2], 'big'), int.from_bytes(b[2:], 'big')]

def regs_to_float(reg1, reg2):
    """Convert 2 registers back to float"""
    b = reg1.to_bytes(2, 'big') + reg2.to_bytes(2, 'big')
    return struct.unpack('>f', b)[0]

class ModbusSimulator:
    def __init__(self, host='localhost', port=502, protocol='tcp'):
        self.host = host
        self.port = port
        self.protocol = protocol.lower()  # 'tcp' hoặc 'rtu'
        
        # Khởi tạo các giá trị ban đầu
        self.base_values = {
            0: 12.3,     # Silica
            2: 50.0,     # Flow permeate
            4: 2.3,      # Flow concentrate
            6: 10.0,     # Concentration factor
            8: 1.5,      # Absorbance
            10: 2.1,     # Photometer voltage
            12: 300,     # Signal period
            14: 99,      # Remaining reagents
            16: 30,      # SilTrace Temperature
            18: 80,      # SilTrace PWM
            20: 32,      # Case temperature
            22: 4.5,     # Signal Output 1
            24: 4.9,     # Signal Output 2
            26: 1234,    # INSTRUMENT_STATUS (int value)
        }
        
        # Tạo datastore với 1000 holding registers
        self.datastore = self._create_datastore()
        
    def _create_datastore(self):
        """Tạo datastore cho Modbus server"""
        # Khởi tạo 1000 holding registers với giá trị 0
        holding_registers = [0] * 1000
        
        # Điền các giá trị từ base_values
        for addr, value in self.base_values.items():
            if addr == 26:  # INSTRUMENT_STATUS - giá trị integer
                holding_registers[addr] = int(value)
                holding_registers[addr + 1] = 0
            else:  # Các giá trị float
                regs = float_to_regs(float(value))
                holding_registers[addr] = regs[0]
                holding_registers[addr + 1] = regs[1]
        
        # Tạo datastore
        store = ModbusSlaveContext(
            di=ModbusSequentialDataBlock(0, [0] * 100),      # Discrete Inputs
            co=ModbusSequentialDataBlock(0, [0] * 100),      # Coils
            hr=ModbusSequentialDataBlock(0, holding_registers), # Holding Registers
            ir=ModbusSequentialDataBlock(0, [0] * 100)       # Input Registers
        )
        
        return ModbusServerContext(slaves=store, single=True)
    
    def update_values(self):
        """Cập nhật giá trị với một chút biến động ngẫu nhiên"""
        slave_context = self.datastore[0]
        
        for addr, base_value in self.base_values.items():
            if addr == 26:  # INSTRUMENT_STATUS - không thay đổi
                continue
                
            # Thêm biến động ngẫu nhiên ±5%
            variation = random.uniform(-0.05, 0.05)
            new_value = base_value * (1 + variation)
            
            # Chuyển đổi thành registers
            regs = float_to_regs(new_value)
            
            # Cập nhật vào datastore
            slave_context.setValues(3, addr, regs)  # Function code 3 = Holding Registers
            
        log.info("Updated values with random variations")
    
    async def start_server(self):
        """Khởi động Modbus server"""
        # Cấu hình device identification
        identity = ModbusDeviceIdentification()
        identity.VendorName = 'Modbus Simulator'
        identity.ProductCode = 'MS'
        identity.VendorUrl = 'http://github.com/modbus-simulator'
        identity.ProductName = 'Modbus Simulator'
        identity.ModelName = 'Modbus Simulator'
        identity.MajorMinorRevision = '1.0'

        # Chọn framer dựa trên protocol
        if self.protocol == 'rtu':
            framer = ModbusRtuFramer
            protocol_name = "RTU over TCP"
        else:
            framer = ModbusTcpFramer
            protocol_name = "TCP"

        log.info(f"Starting Modbus {protocol_name} server on {self.host}:{self.port}")
        log.info("Available data addresses:")
        for addr, value in self.base_values.items():
            if addr == 26:
                log.info(f"  Address {addr}-{addr+1}: INSTRUMENT_STATUS = {value} (integer)")
            else:
                log.info(f"  Address {addr}-{addr+1}: {value} (float)")

        # Khởi động server với framer phù hợp
        await StartAsyncTcpServer(
            context=self.datastore,
            identity=identity,
            address=(self.host, self.port),
            framer=framer
        )

async def main():
    """Hàm main để chạy simulator"""
    import argparse

    parser = argparse.ArgumentParser(description='Modbus TCP/RTU Simulator')
    parser.add_argument('--host', default='localhost', help='Host address (default: localhost)')
    parser.add_argument('--port', type=int, default=502, help='Port number (default: 502)')
    parser.add_argument('--protocol', choices=['tcp', 'rtu'], default='tcp',
                       help='Protocol type: tcp (Modbus TCP) or rtu (RTU over TCP) (default: tcp)')
    parser.add_argument('--update-interval', type=int, default=5,
                       help='Value update interval in seconds (default: 5)')

    args = parser.parse_args()

    # Tạo simulator
    simulator = ModbusSimulator(host=args.host, port=args.port, protocol=args.protocol)

    # Tạo task để cập nhật giá trị định kỳ
    async def update_loop():
        while True:
            await asyncio.sleep(args.update_interval)
            simulator.update_values()

    # Khởi động update task
    update_task = asyncio.create_task(update_loop())

    try:
        # Khởi động server (blocking call)
        await simulator.start_server()
    except KeyboardInterrupt:
        log.info("Server stopped by user")
    except Exception as e:
        log.error(f"Server error: {e}")
    finally:
        update_task.cancel()

if __name__ == "__main__":
    asyncio.run(main())
