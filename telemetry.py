from paho.mqtt import client as mqtt
import ssl
import certifi
import time

# Sử dụng certifi để lấy bundle chứng chỉ CA
path_to_root_cert = certifi.where()
device_id = "Rasp0001"
sas_token = "SharedAccessSignature sr=DEMOIOTHUB.azure-devices.net%2Fdevices%2FRasp0001&sig=AbVo%2FkSTDERQXYwJeXrOd3zv5%2BaaQ8Z5c5JS55KIm6A%3D&se=1749630559"
iot_hub_name = "DEMOIOTHUB"


def on_connect(client, userdata, flags, rc, properties=None):
    if rc == 0:
        print("Device connected successfully")
        client.connected_flag = True
    else:
        print(f"Device connection failed with result code: {rc}")
        client.connected_flag = False

def on_disconnect(client, userdata, flags, rc, properties=None):
    print(f"Device disconnected with result code: {rc}")
    client.connected_flag = False
    if rc != 0:
        print("Unexpected disconnection. Reconnecting...")

def on_publish(client, userdata, mid, rs, properties=None):
    print(f"Message published with mid: {mid}")

def on_log(client, userdata, level, buf):
    print(f"Log: {buf}")


client = mqtt.Client(client_id=device_id, protocol=mqtt.MQTTv311, callback_api_version=mqtt.CallbackAPIVersion.VERSION2)
# Thêm flag để theo dõi trạng thái kết nối
client.connected_flag = False

client.on_connect = on_connect
client.on_disconnect = on_disconnect
client.on_publish = on_publish
client.on_log = on_log  # Để debug

client.username_pw_set(username=iot_hub_name+".azure-devices.net/" +
                       device_id + "/?api-version=2021-04-12", password=sas_token)

client.tls_set(ca_certs=path_to_root_cert, certfile=None, keyfile=None,
               cert_reqs=ssl.CERT_REQUIRED, tls_version=ssl.PROTOCOL_TLSv1_2, ciphers=None)
client.tls_insecure_set(False)

try:
    client.connect(f"{iot_hub_name}.azure-devices.net", port=8883, keepalive=60)
    client.loop_start()
    
    # Đợi kết nối
    timeout = time.time() + 10
    while not client.connected_flag and time.time() < timeout:
        time.sleep(0.1)
    
    if client.connected_flag:
        print("Publishing message...")
        client.message_published = False  # Reset flag trước khi publish
        
        result = client.publish(f"devices/{device_id}/messages/events/", 
                              '{"id":123, "timestamp": "' + str(time.time()) + '"}', 
                              qos=1)
        
        # Đợi message được publish thành công
        timeout = time.time() + 10
        while not client.message_published and time.time() < timeout:
            time.sleep(0.1)
        
        if client.message_published:
            print("Message confirmed published")
        else:
            print("Message publish timeout")
        
        time.sleep(2)  # Đợi thêm chút
    
except Exception as e:
    print(f"Error: {e}")
finally:
    print("Disconnecting...")
    client.loop_stop()
    client.disconnect()